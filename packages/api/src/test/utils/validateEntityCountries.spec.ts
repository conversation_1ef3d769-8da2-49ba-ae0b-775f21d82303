import { suite, test } from "mocha-typescript";
import { expect, should } from "chai";
import * as Errors from "../../skywind/errors";
import { BaseEntity, ChildEntity } from "../../skywind/entities/entity";
import { Jurisdiction } from "../../skywind/entities/jurisdiction";
import { EntitySettings } from "../../skywind/entities/settings";
import {
    validateCreateDefaultCountry,
    validateUpdateDefaultCountry,
    validateCreateCountries,
    validateUpdateCountries,
    validateCountryRestrictions
} from "../../skywind/utils/validateEntityCountries";

const mockEntitySettings: EntitySettings = {
    emailTemplates: {
        passwordRecovery: {
            from: "",
            subject: "",
            html: ""
        },
        changeEmail: {
            from: "",
            subject: "",
            html: ""
        }
    }
};

@suite
class ValidateEntityCountriesSpec {
    public static parentEntity = {
        countries: ["US", "GB", "CA"]
    } as Partial<BaseEntity> as BaseEntity;

    public static childEntity = {
        parent: 1, // Mock parent ID
        defaultCountry: "US",
        countries: ["US", "GB"],
        getCountries: () => ["US", "GB"],
        child: []
    } as Partial<ChildEntity> as ChildEntity;

    public static jurisdiction = {
        allowedCountries: ["US", "GB", "CA", "AU"],
        restrictedCountries: ["RU", "CN"],
        defaultCountry: undefined
    } as Partial<Jurisdiction> as Jurisdiction;

    public static jurisdictionWithDefault = {
        allowedCountries: ["US", "GB", "CA", "AU"],
        restrictedCountries: ["RU", "CN"],
        defaultCountry: "US"
    } as Partial<Jurisdiction> as Jurisdiction;

    public static before() {
        should();
    }

    // validateCreateDefaultCountry tests
    @test
    public createDefaultCountryWithValidCountry() {
        expect(validateCreateDefaultCountry(
            { defaultCountry: "US" },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public createDefaultCountryWithNullAndJurisdictionDefault() {
        expect(validateCreateDefaultCountry(
            { defaultCountry: null },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdictionWithDefault,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public createDefaultCountryWithUndefinedAndJurisdictionDefault() {
        expect(validateCreateDefaultCountry(
            { defaultCountry: undefined },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdictionWithDefault,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public createDefaultCountryWithNullAndNoJurisdictionDefault() {
        expect(() => validateCreateDefaultCountry(
            { defaultCountry: null },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country should be defined in jurisdiction or entity");
    }

    @test
    public createDefaultCountryWithUndefinedAndNoJurisdictionDefault() {
        expect(() => validateCreateDefaultCountry(
            { defaultCountry: undefined },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country should be defined in jurisdiction or entity");
    }

    @test
    public createDefaultCountryWithInvalidCountry() {
        expect(() => validateCreateDefaultCountry(
            { defaultCountry: "INVALID" },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country [INVALID] is invalid");
    }

    // validateUpdateDefaultCountry tests
    @test
    public updateDefaultCountryNoChange() {
        expect(validateUpdateDefaultCountry(
            { defaultCountry: "US" },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public updateDefaultCountryToNull() {
        expect(validateUpdateDefaultCountry(
            { defaultCountry: null },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdictionWithDefault,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public updateDefaultCountryToNullWithoutJurisdictionDefault() {
        expect(() => validateUpdateDefaultCountry(
            { defaultCountry: null },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country should be defined in jurisdiction or entity");
    }

    @test
    public updateDefaultCountryToUndefined() {
        expect(validateUpdateDefaultCountry(
            { defaultCountry: undefined },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public updateDefaultCountryToValidCountry() {
        expect(validateUpdateDefaultCountry(
            { defaultCountry: "GB" },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public updateDefaultCountryToInvalidCountry() {
        expect(() => validateUpdateDefaultCountry(
            { defaultCountry: "INVALID" },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country [INVALID] is invalid");
    }

    // validateCreateCountries tests
    @test
    public createCountriesWithUseCountriesFromJurisdiction() {
        const data = { countries: ["US", "GB"] };
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(validateCreateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            entitySettings
        )).to.not.throw;

        expect(data.countries).to.be.null;
    }

    @test
    public createCountriesWithValidCountries() {
        const data = { countries: ["US", "GB"] };

        expect(validateCreateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public createCountriesWithInvalidArray() {
        const data = { countries: "US" as any };

        expect(() => validateCreateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockEntitySettings
        )).to.throw(Errors.CountriesIsNotArray);
    }

    @test
    public createCountriesWithInvalidCountry() {
        const data = { countries: ["US", "INVALID"] };

        expect(() => validateCreateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Country [INVALID] is invalid");
    }

    // validateUpdateCountries tests
    @test
    public updateCountriesWithUseCountriesFromJurisdiction() {
        const data = { countries: ["US", "GB"] };
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            entitySettings
        )).to.not.throw;

        expect(data.countries).to.be.undefined;
    }

    @test
    public updateCountriesNoChange() {
        const data = { countries: ["US", "GB"] };

        expect(validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public updateCountriesWithValidCountries() {
        const data = { countries: ["US", "CA"] };

        expect(validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public updateCountriesWithInvalidArray() {
        const data = { countries: "US" as any };

        expect(() => validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            mockEntitySettings
        )).to.throw(Errors.CountriesIsNotArray);
    }

    @test
    public updateCountriesWithInvalidCountry() {
        const data = { countries: ["US", "INVALID"] };

        expect(() => validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Country [INVALID] is invalid");
    }

    // validateCountryRestrictions tests
    @test
    public countryRestrictionsWithInvalidCountry() {
        expect(() => validateCountryRestrictions(
            "INVALID",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [INVALID] is invalid");
    }

    @test
    public countryRestrictionsWithJurisdictionAllowedCountries() {
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.not.throw;
    }

    @test
    public countryRestrictionsWithJurisdictionNotInAllowedCountries() {
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(() => validateCountryRestrictions(
            "FR",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [FR] should be in jurisdiction allowed countries");
    }

    @test
    public countryRestrictionsWithJurisdictionRestrictedCountries() {
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(() => validateCountryRestrictions(
            "RU",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [RU] cannot be in jurisdiction restricted countries");
    }

    @test
    public countryRestrictionsWithEntityAllowedCountries() {
        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings,
            "Test country"
        )).to.not.throw;
    }

    @test
    public countryRestrictionsWithEntityNotInAllowedCountries() {
        expect(() => validateCountryRestrictions(
            "FR",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [FR] should be in parent entity countries");
    }

    @test
    public countryRestrictionsWithEntityRestrictedCountries() {
        const entitySettings = { ...mockEntitySettings, restrictedCountries: ["RU", "CN"] };

        expect(() => validateCountryRestrictions(
            "RU",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [RU] cannot be in entity settings restricted countries");
    }

    @test
    public countryRestrictionsWithNullCountry() {
        expect(() => validateCountryRestrictions(
            null,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [null] is invalid");
    }

    @test
    public countryRestrictionsWithUndefinedCountry() {
        expect(() => validateCountryRestrictions(
            undefined,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [undefined] is invalid");
    }

    // Edge cases and complex scenarios
    @test
    public createDefaultCountryWithEmptyString() {
        expect(() => validateCreateDefaultCountry(
            { defaultCountry: "" },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country [] is invalid");
    }

    @test
    public updateDefaultCountryWithEmptyString() {
        expect(() => validateUpdateDefaultCountry(
            { defaultCountry: "" },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country [] is invalid");
    }

    @test
    public createCountriesWithEmptyArray() {
        const data = { countries: [] };

        expect(validateCreateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public updateCountriesWithEmptyArray() {
        const data = { countries: [] };

        expect(validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public createCountriesWithNullValue() {
        const data = { countries: null };

        expect(validateCreateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public updateCountriesWithNullValue() {
        const data = { countries: null };

        expect(validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public createCountriesWithUndefinedValue() {
        const data = { countries: undefined };

        expect(validateCreateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public updateCountriesWithUndefinedValue() {
        const data = { countries: undefined };

        expect(validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public countryRestrictionsWithJurisdictionEmptyAllowedCountries() {
        const jurisdiction = { ...ValidateEntityCountriesSpec.jurisdiction, allowedCountries: [] };
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            jurisdiction,
            entitySettings,
            "Test country"
        )).to.not.throw;
    }

    @test
    public countryRestrictionsWithJurisdictionEmptyRestrictedCountries() {
        const jurisdiction = { ...ValidateEntityCountriesSpec.jurisdiction, restrictedCountries: [] };
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            jurisdiction,
            entitySettings,
            "Test country"
        )).to.not.throw;
    }

    @test
    public countryRestrictionsWithJurisdictionUndefinedAllowedCountries() {
        const jurisdiction = { ...ValidateEntityCountriesSpec.jurisdiction, allowedCountries: undefined };
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            jurisdiction,
            entitySettings,
            "Test country"
        )).to.not.throw;
    }

    @test
    public countryRestrictionsWithJurisdictionUndefinedRestrictedCountries() {
        const jurisdiction = { ...ValidateEntityCountriesSpec.jurisdiction, restrictedCountries: undefined };
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            jurisdiction,
            entitySettings,
            "Test country"
        )).to.not.throw;
    }

    @test
    public countryRestrictionsWithEntityEmptyRestrictedCountries() {
        const entitySettings = { ...mockEntitySettings, restrictedCountries: [] };

        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.not.throw;
    }

    @test
    public countryRestrictionsWithEntityUndefinedRestrictedCountries() {
        const entitySettings = { ...mockEntitySettings, restrictedCountries: undefined };

        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.not.throw;
    }

    // Tests for specific user requirements
    @test
    public createDefaultCountryAllowNullWhenJurisdictionHasDefault() {
        // on create, allow null (or undefined) if jurisdiction.defaultCountry set (no useUseCountriesFromJurisdiction check)
        expect(validateCreateDefaultCountry(
            { defaultCountry: null },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdictionWithDefault,
            mockEntitySettings
        )).to.not.throw;

        expect(validateCreateDefaultCountry(
            { defaultCountry: undefined },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdictionWithDefault,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public updateDefaultCountryAllowNullOnlyWhenJurisdictionHasDefault() {
        // on update, allow null only if jurisdiction.defaultCountry set (no useUseCountriesFromJurisdiction check), allow undefined in any case
        expect(validateUpdateDefaultCountry(
            { defaultCountry: null },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdictionWithDefault,
            mockEntitySettings
        )).to.not.throw;

        expect(() => validateUpdateDefaultCountry(
            { defaultCountry: null },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "Default country should be defined in jurisdiction or entity");

        // undefined should always be allowed on update
        expect(validateUpdateDefaultCountry(
            { defaultCountry: undefined },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings
        )).to.not.throw;

        expect(validateUpdateDefaultCountry(
            { defaultCountry: undefined },
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            ValidateEntityCountriesSpec.jurisdictionWithDefault,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public createCountriesIgnoreValuesWhenUseCountriesFromJurisdiction() {
        // on create, if useCountriesFromJurisdiction=true in parent entity - ignore values (set to null)
        const data = { countries: ["US", "GB", "CA"] };
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        validateCreateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            entitySettings
        );

        expect(data.countries).to.be.null;
    }

    @test
    public updateCountriesIgnoreValuesWhenUseCountriesFromJurisdiction() {
        // on update, if useCountriesFromJurisdiction=true - ignore new values (set to default)
        const data = { countries: ["US", "GB", "CA"] };
        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };

        validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.childEntity,
            entitySettings
        );

        expect(data.countries).to.be.undefined;
    }

    @test
    public countryRestrictionsAllowCountryInEntityCountries() {
        // allow country if in entity.countries or (if entitySettings.useCountriesFromJurisdiction=true - jurisdiction.allowedCountries)
        expect(validateCountryRestrictions(
            "US",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings,
            "Test country"
        )).to.not.throw;

        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };
        expect(validateCountryRestrictions(
            "AU",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.not.throw;
    }

    @test
    public countryRestrictionsDisallowCountryNotInEntityCountries() {
        expect(() => validateCountryRestrictions(
            "FR",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            mockEntitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [FR] should be in parent entity countries");

        const entitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };
        expect(() => validateCountryRestrictions(
            "FR",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [FR] should be in jurisdiction allowed countries");
    }

    @test
    public countryRestrictionsDisallowRestrictedCountries() {
        // allow country if not in entitySettings.restrictedCountries or (if entitySettings.useCountriesFromJurisdiction=true - jurisdiction.restrictedCountries)
        const entitySettings = { ...mockEntitySettings, restrictedCountries: ["RU"] };
        expect(() => validateCountryRestrictions(
            "RU",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            entitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [RU] cannot be in entity settings restricted countries");

        const jurisdictionEntitySettings = { ...mockEntitySettings, useCountriesFromJurisdiction: true };
        expect(() => validateCountryRestrictions(
            "RU",
            ValidateEntityCountriesSpec.parentEntity,
            ValidateEntityCountriesSpec.jurisdiction,
            jurisdictionEntitySettings,
            "Test country"
        )).to.throw(Errors.ValidationError, "Test country [RU] cannot be in jurisdiction restricted countries");
    }

    // Test for child entity removal validation
    @test
    public updateCountriesPreventRemovalWhenUsedByChildren() {
        // Mock the child entity to have the countryExists method
        const mockChildEntity = {
            ...ValidateEntityCountriesSpec.childEntity,
            child: [{
                title: "Child Entity",
                path: "/child",
                name: "child-entity",
                countryExists: (code: string) => code === "GB"
            }],
            getCountries: () => ["US", "GB"]
        };

        const data = { countries: ["US"] }; // Removing GB

        expect(() => validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockChildEntity as any,
            mockEntitySettings
        )).to.throw(Errors.ValidationError, "This country code GB in use in child entities. You cannot remove it.");
    }

    @test
    public updateCountriesAllowRemovalWhenNotUsedByChildren() {
        // Mock the child entity to have no children using the country
        const mockChildEntity = {
            ...ValidateEntityCountriesSpec.childEntity,
            child: [],
            getCountries: () => ["US", "GB"]
        };

        const data = { countries: ["US"] }; // Removing GB

        expect(validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockChildEntity as any,
            mockEntitySettings
        )).to.not.throw;
    }

    @test
    public updateCountriesAllowRemovalWhenChildrenDontUseCountry() {
        // Mock the child entity to have children that don't use the removed country
        const mockChildEntity = {
            ...ValidateEntityCountriesSpec.childEntity,
            child: [{
                title: "Child Entity",
                path: "/child",
                name: "child-entity",
                countryExists: (code: string) => code === "US" // Only uses US, not GB
            }],
            getCountries: () => ["US", "GB"]
        };

        const data = { countries: ["US"] }; // Removing GB

        expect(validateUpdateCountries(
            data,
            ValidateEntityCountriesSpec.parentEntity,
            mockChildEntity as any,
            mockEntitySettings
        )).to.not.throw;
    }
}
